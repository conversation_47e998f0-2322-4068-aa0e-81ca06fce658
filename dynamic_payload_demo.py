#!/usr/bin/env python3
"""
动态负载演示脚本
展示如何在仿真运行时动态修改负载参数
"""

import mujoco
import numpy as np
import time
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation

class DynamicPayloadDemo:
    def __init__(self, model_path="go2_with_payload_generated.xml"):
        """
        初始化动态负载演示
        
        Args:
            model_path: 机器人模型文件路径
        """
        self.model_path = model_path
        self.model = None
        self.data = None
        self.payload_bodies = []
        self.original_masses = {}
        self.load_model()
        
    def load_model(self):
        """加载MuJoCo模型"""
        try:
            self.model = mujoco.MjModel.from_xml_path(self.model_path)
            self.data = mujoco.MjData(self.model)
            print(f"成功加载模型: {self.model_path}")
            self.find_payload_bodies()
        except Exception as e:
            print(f"加载模型失败: {e}")
            
    def find_payload_bodies(self):
        """查找负载身体"""
        self.payload_bodies = []
        for i in range(self.model.nbody):
            body_name = mujoco.mj_id2name(self.model, mujoco.mjtObj.mjOBJ_BODY, i)
            if body_name and "payload" in body_name:
                self.payload_bodies.append(i)
                self.original_masses[i] = self.model.body_mass[i]
                print(f"找到负载身体: {body_name} (ID: {i}, 质量: {self.model.body_mass[i]:.3f} kg)")
    
    def set_payload_mass(self, body_id, new_mass):
        """设置负载质量"""
        if body_id < self.model.nbody:
            old_mass = self.model.body_mass[body_id]
            self.model.body_mass[body_id] = new_mass
            body_name = mujoco.mj_id2name(self.model, mujoco.mjtObj.mjOBJ_BODY, body_id)
            print(f"更新 {body_name} 质量: {old_mass:.3f} -> {new_mass:.3f} kg")
    
    def get_total_mass(self):
        """计算总质量"""
        return np.sum(self.model.body_mass)
    
    def get_center_of_mass(self):
        """计算系统质心"""
        mujoco.mj_forward(self.model, self.data)
        total_mass = 0
        com = np.zeros(3)
        
        for i in range(self.model.nbody):
            if self.model.body_mass[i] > 0:
                body_pos = self.data.xpos[i]
                body_mass = self.model.body_mass[i]
                com += body_pos * body_mass
                total_mass += body_mass
        
        if total_mass > 0:
            com /= total_mass
        
        return com
    
    def simulate_step(self):
        """执行一步仿真"""
        mujoco.mj_step(self.model, self.data)
    
    def reset_simulation(self):
        """重置仿真"""
        mujoco.mj_resetData(self.model, self.data)
        mujoco.mj_forward(self.model, self.data)
    
    def demo_mass_variation(self, duration=10.0, frequency=0.5):
        """演示质量变化对机器人的影响"""
        print(f"\n开始质量变化演示 (持续时间: {duration}s)")
        
        # 记录数据
        times = []
        masses = []
        com_positions = []
        base_positions = []
        
        start_time = time.time()
        step_count = 0
        
        while time.time() - start_time < duration:
            current_time = time.time() - start_time
            
            # 动态改变负载质量
            for body_id in self.payload_bodies:
                base_mass = self.original_masses[body_id]
                # 使用正弦波变化质量
                new_mass = base_mass * (1.0 + 0.5 * np.sin(2 * np.pi * frequency * current_time))
                self.set_payload_mass(body_id, new_mass)
            
            # 执行仿真步骤
            self.simulate_step()
            
            # 记录数据
            if step_count % 50 == 0:  # 每50步记录一次
                times.append(current_time)
                masses.append(self.get_total_mass())
                com_positions.append(self.get_center_of_mass().copy())
                
                # 获取基座位置
                base_id = mujoco.mj_name2id(self.model, mujoco.mjtObj.mjOBJ_BODY, "base_link")
                if base_id >= 0:
                    base_positions.append(self.data.xpos[base_id].copy())
                else:
                    base_positions.append(np.zeros(3))
            
            step_count += 1
        
        return times, masses, com_positions, base_positions
    
    def plot_results(self, times, masses, com_positions, base_positions):
        """绘制结果"""
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        fig.suptitle('动态负载仿真结果', fontsize=16)
        
        # 质量变化
        axes[0, 0].plot(times, masses)
        axes[0, 0].set_title('总质量变化')
        axes[0, 0].set_xlabel('时间 (s)')
        axes[0, 0].set_ylabel('质量 (kg)')
        axes[0, 0].grid(True)
        
        # 质心位置变化
        com_array = np.array(com_positions)
        axes[0, 1].plot(times, com_array[:, 0], label='X')
        axes[0, 1].plot(times, com_array[:, 1], label='Y')
        axes[0, 1].plot(times, com_array[:, 2], label='Z')
        axes[0, 1].set_title('系统质心位置')
        axes[0, 1].set_xlabel('时间 (s)')
        axes[0, 1].set_ylabel('位置 (m)')
        axes[0, 1].legend()
        axes[0, 1].grid(True)
        
        # 基座位置变化
        base_array = np.array(base_positions)
        axes[1, 0].plot(times, base_array[:, 2])  # Z轴位置
        axes[1, 0].set_title('机器人基座高度')
        axes[1, 0].set_xlabel('时间 (s)')
        axes[1, 0].set_ylabel('高度 (m)')
        axes[1, 0].grid(True)
        
        # 质心轨迹 (XY平面)
        axes[1, 1].plot(com_array[:, 0], com_array[:, 1])
        axes[1, 1].set_title('质心轨迹 (XY平面)')
        axes[1, 1].set_xlabel('X (m)')
        axes[1, 1].set_ylabel('Y (m)')
        axes[1, 1].grid(True)
        axes[1, 1].axis('equal')
        
        plt.tight_layout()
        plt.savefig('dynamic_payload_results.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def compare_payload_scenarios(self):
        """比较不同负载场景"""
        scenarios = [
            {"name": "无负载", "masses": [0.0] * len(self.payload_bodies)},
            {"name": "轻负载", "masses": [1.0] * len(self.payload_bodies)},
            {"name": "中等负载", "masses": [3.0] * len(self.payload_bodies)},
            {"name": "重负载", "masses": [5.0] * len(self.payload_bodies)},
        ]
        
        results = {}
        
        for scenario in scenarios:
            print(f"\n测试场景: {scenario['name']}")
            
            # 设置负载质量
            for i, body_id in enumerate(self.payload_bodies):
                if i < len(scenario['masses']):
                    self.set_payload_mass(body_id, scenario['masses'][i])
            
            # 重置仿真
            self.reset_simulation()
            
            # 记录初始状态
            total_mass = self.get_total_mass()
            com = self.get_center_of_mass()
            
            results[scenario['name']] = {
                'total_mass': total_mass,
                'center_of_mass': com
            }
            
            print(f"  总质量: {total_mass:.3f} kg")
            print(f"  质心位置: [{com[0]:.3f}, {com[1]:.3f}, {com[2]:.3f}] m")
        
        return results
    
    def print_model_info(self):
        """打印模型信息"""
        print(f"\n=== 模型信息 ===")
        print(f"身体数量: {self.model.nbody}")
        print(f"关节数量: {self.model.njnt}")
        print(f"执行器数量: {self.model.nu}")
        print(f"传感器数量: {self.model.nsensor}")
        
        print(f"\n=== 负载身体信息 ===")
        for body_id in self.payload_bodies:
            body_name = mujoco.mj_id2name(self.model, mujoco.mjtObj.mjOBJ_BODY, body_id)
            mass = self.model.body_mass[body_id]
            inertia = self.model.body_inertia[body_id]
            print(f"{body_name}: 质量={mass:.3f}kg, 惯性=[{inertia[0]:.6f}, {inertia[1]:.6f}, {inertia[2]:.6f}]")

def main():
    """主函数"""
    print("动态负载演示程序")
    print("=" * 50)
    
    # 创建演示实例
    demo = DynamicPayloadDemo()
    
    if demo.model is None:
        print("无法加载模型，退出程序")
        return
    
    # 打印模型信息
    demo.print_model_info()
    
    # 比较不同负载场景
    print("\n1. 比较不同负载场景...")
    scenario_results = demo.compare_payload_scenarios()
    
    # 动态质量变化演示
    print("\n2. 动态质量变化演示...")
    try:
        times, masses, com_positions, base_positions = demo.demo_mass_variation(
            duration=5.0, frequency=0.2
        )
        
        # 绘制结果
        print("\n3. 绘制结果...")
        demo.plot_results(times, masses, com_positions, base_positions)
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
    
    print("\n演示完成！")

if __name__ == "__main__":
    main()
