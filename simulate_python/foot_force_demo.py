"""
足端力传感器数据传输和可视化演示程序
展示如何在unitree_mujoco仿真环境中获取和显示go2机器人的足端力数据
"""

import time
import mujoco
import mujoco.viewer
import numpy as np
from threading import Thread
import threading

from unitree_sdk2py.core.channel import ChannelFactoryInitialize, ChannelSubscriber
from unitree_sdk2py.idl.unitree_go.msg.dds_ import LowState_
from foot_force_visualizer import FootForceVisualizer

import config


class FootForceDemo:
    """足端力演示类"""
    
    def __init__(self):
        """初始化演示程序"""
        # 初始化MuJoCo模型和数据
        self.mj_model = mujoco.MjModel.from_xml_path(config.ROBOT_SCENE)
        self.mj_data = mujoco.MjData(self.mj_model)
        
        # 初始化足端力可视化器
        self.force_visualizer = FootForceVisualizer(self.mj_model, self.mj_data)
        
        # 初始化SDK2通信
        ChannelFactoryInitialize(0, "lo")
        
        # 订阅LowState消息
        self.low_state_subscriber = ChannelSubscriber("rt/lowstate", LowState_)
        self.low_state_subscriber.Init(self.low_state_handler, 10)
        
        # 存储最新的足端力数据
        self.latest_foot_forces = {}
        self.data_lock = threading.Lock()
        
        # 启动MuJoCo viewer
        self.viewer = mujoco.viewer.launch_passive(
            self.mj_model, 
            self.mj_data,
            key_callback=self.key_callback
        )
        
        # 控制标志
        self.running = True
        self.show_force_vectors = True
        self.show_force_data = False
        
    def low_state_handler(self, msg: LowState_):
        """
        处理接收到的LowState消息
        
        Args:
            msg: LowState消息
        """
        with self.data_lock:
            # 提取足端力数据
            if hasattr(msg, 'foot_force') and len(msg.foot_force) >= 12:
                self.latest_foot_forces = {
                    'FL': np.array([msg.foot_force[0], msg.foot_force[1], msg.foot_force[2]]),
                    'FR': np.array([msg.foot_force[3], msg.foot_force[4], msg.foot_force[5]]),
                    'RL': np.array([msg.foot_force[6], msg.foot_force[7], msg.foot_force[8]]),
                    'RR': np.array([msg.foot_force[9], msg.foot_force[10], msg.foot_force[11]])
                }
                
    def key_callback(self, keycode):
        """
        键盘回调函数
        
        Args:
            keycode: 按键代码
        """
        if keycode == ord('f') or keycode == ord('F'):
            # 切换力向量显示
            self.show_force_vectors = not self.show_force_vectors
            print(f"力向量显示: {'开启' if self.show_force_vectors else '关闭'}")
            
        elif keycode == ord('d') or keycode == ord('D'):
            # 切换数据打印
            self.show_force_data = not self.show_force_data
            print(f"数据打印: {'开启' if self.show_force_data else '关闭'}")
            
        elif keycode == ord('r') or keycode == ord('R'):
            # 重置机器人姿态
            mujoco.mj_resetDataKeyframe(self.mj_model, self.mj_data, 0)
            print("机器人姿态已重置")
            
        elif keycode == ord('q') or keycode == ord('Q'):
            # 退出程序
            self.running = False
            print("退出程序")
            
        elif keycode == ord('h') or keycode == ord('H'):
            # 显示帮助信息
            self.print_help()
            
    def print_help(self):
        """打印帮助信息"""
        print("\n=== 足端力演示程序控制说明 ===")
        print("F/f: 切换足端力向量显示")
        print("D/d: 切换足端力数据打印")
        print("R/r: 重置机器人姿态")
        print("H/h: 显示此帮助信息")
        print("Q/q: 退出程序")
        print("鼠标: 旋转和缩放视角")
        print("================================\n")
        
    def update_simulation(self):
        """更新仿真"""
        while self.running and self.viewer.is_running():
            # 执行仿真步骤
            mujoco.mj_step(self.mj_model, self.mj_data)
            
            # 更新可视化
            if self.show_force_vectors:
                # 这里可以添加自定义的力向量可视化
                pass
                
            # 打印足端力数据
            if self.show_force_data:
                self.print_current_forces()
                
            # 同步viewer
            self.viewer.sync()
            
            # 控制仿真频率
            time.sleep(self.mj_model.opt.timestep)
            
    def print_current_forces(self):
        """打印当前足端力数据"""
        # 从仿真中获取力数据
        sim_forces = self.force_visualizer.get_foot_forces()
        
        # 从SDK消息中获取力数据
        with self.data_lock:
            sdk_forces = self.latest_foot_forces.copy()
            
        print("\n=== 足端力数据对比 ===")
        print("足端 | 仿真数据 (N) | SDK数据 (N) | 大小 (N)")
        print("-" * 60)
        
        for foot_name in ['FL', 'FR', 'RL', 'RR']:
            sim_force = sim_forces.get(foot_name, np.zeros(3))
            sdk_force = sdk_forces.get(foot_name, np.zeros(3))
            
            sim_mag = np.linalg.norm(sim_force)
            sdk_mag = np.linalg.norm(sdk_force)
            
            print(f"{foot_name:2s} | [{sim_force[0]:6.1f}, {sim_force[1]:6.1f}, {sim_force[2]:6.1f}] | "
                  f"[{sdk_force[0]:6.1f}, {sdk_force[1]:6.1f}, {sdk_force[2]:6.1f}] | "
                  f"仿真:{sim_mag:6.1f} SDK:{sdk_mag:6.1f}")
        print()
        
    def run_demo(self):
        """运行演示程序"""
        print("=== Go2足端力传感器演示程序 ===")
        print("正在启动...")
        
        # 打印帮助信息
        self.print_help()
        
        # 启动仿真线程
        sim_thread = Thread(target=self.update_simulation, daemon=True)
        sim_thread.start()
        
        print("演示程序已启动，按 H 查看控制说明")
        
        try:
            # 主循环
            while self.running and self.viewer.is_running():
                time.sleep(0.1)
                
        except KeyboardInterrupt:
            print("\n接收到中断信号，正在退出...")
            
        finally:
            self.running = False
            if self.viewer.is_running():
                self.viewer.close()
            print("演示程序已退出")


def create_foot_force_test_scenario():
    """
    创建足端力测试场景
    在机器人足端添加一些外力来测试传感器
    """
    print("创建足端力测试场景...")
    
    # 这里可以添加一些测试逻辑
    # 例如：在特定时间对足端施加外力
    pass


def analyze_sensor_data_flow():
    """
    分析传感器数据流
    验证从MuJoCo到SDK2的数据传输路径
    """
    print("\n=== 传感器数据流分析 ===")
    
    # 加载模型
    model = mujoco.MjModel.from_xml_path(config.ROBOT_SCENE)
    data = mujoco.MjData(model)
    
    print(f"模型中的传感器总数: {model.nsensor}")
    print(f"传感器数据总维度: {model.nsensordata}")
    
    # 分析传感器布局
    sensor_data_idx = 0
    print("\n传感器布局:")
    print("索引范围 | 传感器名称 | 类型 | 维度")
    print("-" * 50)
    
    for i in range(model.nsensor):
        sensor_name = mujoco.mj_id2name(model, mujoco.mjtObj.mjOBJ_SENSOR, i)
        sensor_type = model.sensor_type[i]
        sensor_dim = model.sensor_dim[i]
        
        end_idx = sensor_data_idx + sensor_dim - 1
        print(f"{sensor_data_idx:3d}-{end_idx:3d}  | {sensor_name:12s} | {sensor_type:4d} | {sensor_dim}")
        
        sensor_data_idx += sensor_dim
        
    print(f"\n足端力传感器预期索引位置:")
    motor_sensors = 3 * model.nu  # 电机传感器
    imu_sensors = 16  # IMU传感器（四元数4 + 陀螺仪3 + 加速度3 + 位置3 + 速度3）
    foot_force_start = motor_sensors + imu_sensors
    
    print(f"电机传感器: 0-{motor_sensors-1}")
    print(f"IMU传感器: {motor_sensors}-{motor_sensors + imu_sensors - 1}")
    print(f"足端力传感器: {foot_force_start}-{foot_force_start + 11}")


if __name__ == "__main__":
    # 分析传感器数据流
    analyze_sensor_data_flow()
    
    # 创建并运行演示程序
    demo = FootForceDemo()
    demo.run_demo()
