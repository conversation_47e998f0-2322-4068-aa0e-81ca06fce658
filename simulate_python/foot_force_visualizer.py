"""
足端力可视化模块
用于在MuJoCo仿真环境中实时显示go2机器人的足端力传感器数据
"""

import numpy as np
import mujoco
from typing import Optional, List, Tuple


class FootForceVisualizer:
    """足端力可视化器"""
    
    def __init__(self, model: mujoco.MjModel, data: mujoco.MjData):
        """
        初始化足端力可视化器
        
        Args:
            model: MuJoCo模型
            data: MuJoCo数据
        """
        self.model = model
        self.data = data
        
        # 足端力传感器配置
        self.foot_names = ["FL", "FR", "RL", "RR"]  # 前左、前右、后左、后右
        self.foot_force_sensors = ["FL_force", "FR_force", "RL_force", "RR_force"]
        self.foot_sites = ["ft_site_FL", "ft_site_FR", "ft_site_RL", "ft_site_RR"]
        
        # 可视化参数
        self.force_scale = 0.001  # 力向量缩放因子 (m/N)
        self.min_force_threshold = 1.0  # 最小显示力阈值 (N)
        self.max_force_length = 0.2  # 最大力向量长度 (m)
        
        # 颜色配置 (RGBA)
        self.force_colors = {
            "FL": [1.0, 0.0, 0.0, 0.8],  # 红色 - 前左
            "FR": [0.0, 1.0, 0.0, 0.8],  # 绿色 - 前右
            "RL": [0.0, 0.0, 1.0, 0.8],  # 蓝色 - 后左
            "RR": [1.0, 1.0, 0.0, 0.8],  # 黄色 - 后右
        }
        
        # 获取传感器和site的ID
        self._get_sensor_ids()
        self._get_site_ids()
        
        # 初始化可视化几何体
        self._init_visualization_geoms()
        
    def _get_sensor_ids(self):
        """获取足端力传感器的ID"""
        self.sensor_ids = {}
        self.sensor_data_indices = {}
        
        sensor_data_idx = 0
        for i in range(self.model.nsensor):
            sensor_name = mujoco.mj_id2name(self.model, mujoco.mjtObj.mjOBJ_SENSOR, i)
            sensor_dim = self.model.sensor_dim[i]
            
            if sensor_name in self.foot_force_sensors:
                self.sensor_ids[sensor_name] = i
                self.sensor_data_indices[sensor_name] = sensor_data_idx
                
            sensor_data_idx += sensor_dim
            
    def _get_site_ids(self):
        """获取足端site的ID"""
        self.site_ids = {}
        for site_name in self.foot_sites:
            site_id = mujoco.mj_name2id(self.model, mujoco.mjtObj.mjOBJ_SITE, site_name)
            if site_id >= 0:
                self.site_ids[site_name] = site_id
                
    def _init_visualization_geoms(self):
        """初始化可视化几何体"""
        # 这里可以添加自定义的可视化几何体
        # 例如力向量的箭头、力的大小指示器等
        pass
        
    def get_foot_forces(self) -> dict:
        """
        获取当前的足端力数据
        
        Returns:
            dict: 包含各足端力数据的字典
        """
        foot_forces = {}
        
        for i, sensor_name in enumerate(self.foot_force_sensors):
            if sensor_name in self.sensor_data_indices:
                start_idx = self.sensor_data_indices[sensor_name]
                # 力传感器返回3D力向量 [Fx, Fy, Fz]
                force_vector = self.data.sensordata[start_idx:start_idx+3]
                foot_name = self.foot_names[i]
                foot_forces[foot_name] = force_vector.copy()
                
        return foot_forces
        
    def get_foot_positions(self) -> dict:
        """
        获取足端位置
        
        Returns:
            dict: 包含各足端位置的字典
        """
        foot_positions = {}
        
        for i, site_name in enumerate(self.foot_sites):
            if site_name in self.site_ids:
                site_id = self.site_ids[site_name]
                # 获取site的全局位置
                position = self.data.site_xpos[site_id].copy()
                foot_name = self.foot_names[i]
                foot_positions[foot_name] = position
                
        return foot_positions
        
    def calculate_force_magnitude(self, force_vector: np.ndarray) -> float:
        """
        计算力向量的大小
        
        Args:
            force_vector: 3D力向量
            
        Returns:
            float: 力的大小
        """
        return np.linalg.norm(force_vector)
        
    def scale_force_vector(self, force_vector: np.ndarray) -> np.ndarray:
        """
        缩放力向量用于可视化
        
        Args:
            force_vector: 原始力向量
            
        Returns:
            np.ndarray: 缩放后的力向量
        """
        magnitude = self.calculate_force_magnitude(force_vector)
        
        if magnitude < self.min_force_threshold:
            return np.zeros(3)
            
        # 应用缩放因子
        scaled_vector = force_vector * self.force_scale
        
        # 限制最大长度
        scaled_magnitude = np.linalg.norm(scaled_vector)
        if scaled_magnitude > self.max_force_length:
            scaled_vector = scaled_vector * (self.max_force_length / scaled_magnitude)
            
        return scaled_vector
        
    def add_force_arrows_to_scene(self, scene: mujoco.MjvScene):
        """
        向MuJoCo场景添加力向量箭头
        
        Args:
            scene: MuJoCo可视化场景
        """
        foot_forces = self.get_foot_forces()
        foot_positions = self.get_foot_positions()
        
        for foot_name in self.foot_names:
            if foot_name in foot_forces and foot_name in foot_positions:
                force_vector = foot_forces[foot_name]
                foot_position = foot_positions[foot_name]
                
                # 缩放力向量
                scaled_force = self.scale_force_vector(force_vector)
                
                if np.linalg.norm(scaled_force) > 0:
                    # 计算箭头终点
                    arrow_end = foot_position + scaled_force
                    
                    # 添加力向量箭头到场景
                    self._add_arrow_to_scene(
                        scene, 
                        foot_position, 
                        arrow_end, 
                        self.force_colors[foot_name]
                    )
                    
    def _add_arrow_to_scene(self, scene: mujoco.MjvScene, start: np.ndarray, 
                           end: np.ndarray, color: List[float]):
        """
        向场景添加箭头
        
        Args:
            scene: MuJoCo场景
            start: 箭头起点
            end: 箭头终点
            color: 箭头颜色 [R, G, B, A]
        """
        # 使用MuJoCo的线条绘制功能
        if scene.ngeom < scene.maxgeom:
            # 计算箭头方向和长度
            direction = end - start
            length = np.linalg.norm(direction)
            
            if length > 0:
                # 归一化方向向量
                direction_norm = direction / length
                
                # 设置几何体属性
                geom = scene.geoms[scene.ngeom]
                geom.type = mujoco.mjtGeom.mjGEOM_ARROW
                geom.size[:] = [0.005, 0.005, length/2]  # 箭头的半径和长度
                
                # 设置位置（箭头中心）
                geom.pos[:] = (start + end) / 2
                
                # 设置方向（需要计算旋转矩阵）
                self._set_arrow_orientation(geom, direction_norm)
                
                # 设置颜色
                geom.rgba[:] = color
                
                # 设置材质属性
                geom.matid = -1
                geom.category = mujoco.mjtCatBit.mjCAT_DECOR
                
                scene.ngeom += 1
                
    def _set_arrow_orientation(self, geom, direction: np.ndarray):
        """
        设置箭头的方向
        
        Args:
            geom: MuJoCo几何体
            direction: 方向向量
        """
        # 默认箭头沿z轴方向，需要计算旋转矩阵
        z_axis = np.array([0, 0, 1])
        
        # 计算旋转轴和角度
        cross_product = np.cross(z_axis, direction)
        dot_product = np.dot(z_axis, direction)
        
        if np.linalg.norm(cross_product) < 1e-6:
            # 方向向量与z轴平行
            if dot_product > 0:
                # 同向，不需要旋转
                geom.mat[:] = np.eye(3).flatten()
            else:
                # 反向，旋转180度
                geom.mat[:] = np.diag([1, -1, -1]).flatten()
        else:
            # 计算旋转矩阵
            axis = cross_product / np.linalg.norm(cross_product)
            angle = np.arccos(np.clip(dot_product, -1, 1))
            
            # 使用罗德里格斯公式计算旋转矩阵
            cos_angle = np.cos(angle)
            sin_angle = np.sin(angle)
            
            K = np.array([[0, -axis[2], axis[1]],
                         [axis[2], 0, -axis[0]],
                         [-axis[1], axis[0], 0]])
            
            R = np.eye(3) + sin_angle * K + (1 - cos_angle) * np.dot(K, K)
            geom.mat[:] = R.flatten()
            
    def print_force_data(self):
        """打印当前足端力数据（用于调试）"""
        foot_forces = self.get_foot_forces()
        foot_positions = self.get_foot_positions()
        
        print("=== 足端力传感器数据 ===")
        for foot_name in self.foot_names:
            if foot_name in foot_forces:
                force = foot_forces[foot_name]
                magnitude = self.calculate_force_magnitude(force)
                position = foot_positions.get(foot_name, [0, 0, 0])
                
                print(f"{foot_name}: 力={force} N, 大小={magnitude:.2f} N, 位置={position}")
        print()
        
    def update_visualization_parameters(self, force_scale: Optional[float] = None,
                                      min_threshold: Optional[float] = None,
                                      max_length: Optional[float] = None):
        """
        更新可视化参数
        
        Args:
            force_scale: 力向量缩放因子
            min_threshold: 最小显示力阈值
            max_length: 最大力向量长度
        """
        if force_scale is not None:
            self.force_scale = force_scale
        if min_threshold is not None:
            self.min_force_threshold = min_threshold
        if max_length is not None:
            self.max_force_length = max_length
