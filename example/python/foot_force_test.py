"""
足端力传感器测试程序
用于验证go2机器人足端力传感器数据的正确性和完整性
"""

import time
import numpy as np
from unitree_sdk2py.core.channel import ChannelFactoryInitialize, ChannelSubscriber
from unitree_sdk2py.idl.unitree_go.msg.dds_ import LowState_


class FootForceTest:
    """足端力传感器测试类"""
    
    def __init__(self):
        """初始化测试程序"""
        # 初始化SDK2通信
        ChannelFactoryInitialize(0, "lo")
        
        # 订阅LowState消息
        self.low_state_subscriber = ChannelSubscriber("rt/lowstate", LowState_)
        self.low_state_subscriber.Init(self.low_state_handler, 10)
        
        # 数据存储
        self.foot_force_data = []
        self.sample_count = 0
        self.max_samples = 1000
        
        # 足端名称映射
        self.foot_names = ["FL", "FR", "RL", "RR"]  # 前左、前右、后左、后右
        
        print("足端力传感器测试程序已启动")
        print("正在等待数据...")
        
    def low_state_handler(self, msg: LowState_):
        """
        处理LowState消息
        
        Args:
            msg: LowState消息
        """
        self.sample_count += 1
        
        # 检查是否有足端力数据
        if hasattr(msg, 'foot_force'):
            foot_forces = msg.foot_force
            
            # 提取四个足端的力数据
            forces = {
                'FL': [foot_forces[0], foot_forces[1], foot_forces[2]],   # 前左
                'FR': [foot_forces[3], foot_forces[4], foot_forces[5]],   # 前右
                'RL': [foot_forces[6], foot_forces[7], foot_forces[8]],   # 后左
                'RR': [foot_forces[9], foot_forces[10], foot_forces[11]]  # 后右
            }
            
            # 存储数据
            timestamp = time.time()
            self.foot_force_data.append({
                'timestamp': timestamp,
                'sample': self.sample_count,
                'forces': forces
            })
            
            # 实时显示数据
            if self.sample_count % 50 == 0:  # 每50个样本显示一次
                self.print_current_data(forces)
                
            # 限制存储的样本数量
            if len(self.foot_force_data) > self.max_samples:
                self.foot_force_data.pop(0)
                
        else:
            print(f"警告: LowState消息中没有foot_force字段 (样本 {self.sample_count})")
            
    def print_current_data(self, forces):
        """
        打印当前足端力数据
        
        Args:
            forces: 足端力数据字典
        """
        print(f"\n=== 样本 {self.sample_count} ===")
        print("足端 |    Fx (N) |    Fy (N) |    Fz (N) | 合力 (N)")
        print("-" * 55)
        
        for foot_name in self.foot_names:
            if foot_name in forces:
                fx, fy, fz = forces[foot_name]
                magnitude = np.sqrt(fx*fx + fy*fy + fz*fz)
                print(f"{foot_name:2s}   | {fx:8.2f} | {fy:8.2f} | {fz:8.2f} | {magnitude:7.2f}")
            else:
                print(f"{foot_name:2s}   |     N/A  |     N/A  |     N/A  |    N/A")
                
    def analyze_data(self):
        """分析收集到的数据"""
        if not self.foot_force_data:
            print("没有收集到数据")
            return
            
        print(f"\n=== 数据分析 (共 {len(self.foot_force_data)} 个样本) ===")
        
        # 计算统计信息
        for foot_name in self.foot_names:
            forces_x = [data['forces'][foot_name][0] for data in self.foot_force_data if foot_name in data['forces']]
            forces_y = [data['forces'][foot_name][1] for data in self.foot_force_data if foot_name in data['forces']]
            forces_z = [data['forces'][foot_name][2] for data in self.foot_force_data if foot_name in data['forces']]
            
            if forces_x:
                print(f"\n{foot_name} 足端力统计:")
                print(f"  Fx: 平均={np.mean(forces_x):6.2f}, 标准差={np.std(forces_x):6.2f}, 范围=[{np.min(forces_x):6.2f}, {np.max(forces_x):6.2f}]")
                print(f"  Fy: 平均={np.mean(forces_y):6.2f}, 标准差={np.std(forces_y):6.2f}, 范围=[{np.min(forces_y):6.2f}, {np.max(forces_y):6.2f}]")
                print(f"  Fz: 平均={np.mean(forces_z):6.2f}, 标准差={np.std(forces_z):6.2f}, 范围=[{np.min(forces_z):6.2f}, {np.max(forces_z):6.2f}]")
                
                # 计算合力
                magnitudes = [np.sqrt(fx*fx + fy*fy + fz*fz) for fx, fy, fz in zip(forces_x, forces_y, forces_z)]
                print(f"  合力: 平均={np.mean(magnitudes):6.2f}, 标准差={np.std(magnitudes):6.2f}, 范围=[{np.min(magnitudes):6.2f}, {np.max(magnitudes):6.2f}]")
                
    def save_data_to_file(self, filename="foot_force_data.csv"):
        """
        保存数据到文件
        
        Args:
            filename: 文件名
        """
        if not self.foot_force_data:
            print("没有数据可保存")
            return
            
        try:
            with open(filename, 'w') as f:
                # 写入标题行
                f.write("timestamp,sample,FL_x,FL_y,FL_z,FR_x,FR_y,FR_z,RL_x,RL_y,RL_z,RR_x,RR_y,RR_z\n")
                
                # 写入数据
                for data in self.foot_force_data:
                    timestamp = data['timestamp']
                    sample = data['sample']
                    forces = data['forces']
                    
                    line = f"{timestamp},{sample}"
                    for foot_name in self.foot_names:
                        if foot_name in forces:
                            fx, fy, fz = forces[foot_name]
                            line += f",{fx},{fy},{fz}"
                        else:
                            line += ",0,0,0"
                    line += "\n"
                    f.write(line)
                    
            print(f"数据已保存到 {filename}")
            
        except Exception as e:
            print(f"保存数据时出错: {e}")
            
    def run_test(self, duration=30):
        """
        运行测试
        
        Args:
            duration: 测试持续时间（秒）
        """
        print(f"开始测试，持续时间: {duration} 秒")
        print("按 Ctrl+C 提前结束测试")
        
        start_time = time.time()
        
        try:
            while time.time() - start_time < duration:
                time.sleep(0.1)
                
        except KeyboardInterrupt:
            print("\n测试被用户中断")
            
        print(f"\n测试完成，共收集 {len(self.foot_force_data)} 个样本")
        
        # 分析数据
        self.analyze_data()
        
        # 保存数据
        save_choice = input("\n是否保存数据到文件? (y/n): ")
        if save_choice.lower() == 'y':
            filename = input("输入文件名 (默认: foot_force_data.csv): ").strip()
            if not filename:
                filename = "foot_force_data.csv"
            self.save_data_to_file(filename)


def test_foot_force_message_structure():
    """测试足端力消息结构"""
    print("=== 测试LowState消息结构 ===")
    
    # 创建一个LowState消息实例
    from unitree_sdk2py.idl.default import unitree_go_msg_dds__LowState_
    low_state = unitree_go_msg_dds__LowState_()
    
    # 检查是否有foot_force字段
    if hasattr(low_state, 'foot_force'):
        print(f"✓ LowState消息包含foot_force字段")
        print(f"  foot_force数组长度: {len(low_state.foot_force)}")
        
        # 测试设置和读取数据
        test_values = [1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 11.0, 12.0]
        for i, value in enumerate(test_values):
            if i < len(low_state.foot_force):
                low_state.foot_force[i] = value
                
        print("  测试数据写入成功")
        print(f"  读取的数据: {[low_state.foot_force[i] for i in range(min(12, len(low_state.foot_force)))]}")
        
    else:
        print("✗ LowState消息不包含foot_force字段")
        print("  可用字段:", [attr for attr in dir(low_state) if not attr.startswith('_')])
        
    print()


def main():
    """主函数"""
    print("Go2足端力传感器测试程序")
    print("=" * 40)
    
    # 测试消息结构
    test_foot_force_message_structure()
    
    # 创建测试实例
    test = FootForceTest()
    
    # 运行测试
    try:
        test.run_test(duration=30)
    except Exception as e:
        print(f"测试过程中出错: {e}")
        
    print("测试程序结束")


if __name__ == "__main__":
    main()
